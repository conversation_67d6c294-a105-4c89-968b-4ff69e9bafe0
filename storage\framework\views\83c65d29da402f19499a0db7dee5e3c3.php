<?php $__env->startSection('resourcesales'); ?>
<!-- SweetAlert2 CSS -->
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@11.7.32/dist/sweetalert2.min.css">
<!-- SweetAlert2 JS -->
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11.7.32/dist/sweetalert2.all.min.js"></script>

<style>
    .w-fit-content {
        width: fit-content;
    }
    .shadow-kit {
        border: 1px;
        border-radius: 0.5rem;
        background-color: #fff;
    }
    
    .status-ready {
        background-color: #97f784;
        color: #343a40;
    }
    
    .status-not-ready {
        background-color: #eb3124;
        color: white;
    }
    
    .classification-high-demand {
        background-color: #eb3124;
        color: white;
    }
    
    .classification-stable {
        background-color: #97f784;
        color: #343a40;
    }
    
    .classification-low-demand {
        background-color: #feff8c;
        color: #343a40;
    }
    
    .classification-seasonal {
        background-color: #58c0f6;
        color: white;
    }
    
    .classification-overstocked {
        background-color: #510968;
        color: white;
    }
    
    .classification-uncategorized {
        background-color: #6c757d;
        color: white;
    }
    
    .classification-data-insufficient {
        background-color: #f8f9fa;
        color: #343a40;
        border: 1px solid #dee2e6;
    }
    
    .table-responsive {
        max-height: 70vh;
        overflow-y: auto;
    }
    
    .sticky-header {
        position: sticky;
        top: 0;
        z-index: 10;
        background-color: #f8f9fa;
    }
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-refresh data when filters change
    const siteSelect = document.getElementById('site-select');
    const startDateInput = document.getElementById('start-date');
    const endDateInput = document.getElementById('end-date');
    
    function refreshData() {
        const siteId = siteSelect.value;
        const startDate = startDateInput.value;
        const endDate = endDateInput.value;
        
        if (!siteId || !startDate || !endDate) {
            return;
        }
        
        // Show loading
        const tableBody = document.getElementById('analysis-table-body');
        const summaryInfo = document.getElementById('summary-info');
        
        tableBody.innerHTML = '<tr><td colspan="7" class="text-center"><i class="mdi mdi-loading mdi-spin"></i> Memuat data...</td></tr>';
        summaryInfo.innerHTML = '<i class="mdi mdi-loading mdi-spin"></i> Memuat...';
        
        // Fetch data
        fetch(`<?php echo e(route('sales.part-analysis')); ?>?site_id=${siteId}&start_date=${startDate}&end_date=${endDate}`, {
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            updateTable(data);
            updateSummary(data);
        })
        .catch(error => {
            console.error('Error:', error);
            tableBody.innerHTML = '<tr><td colspan="7" class="text-center text-danger">Terjadi kesalahan saat memuat data</td></tr>';
            summaryInfo.innerHTML = 'Error';
        });
    }
    
    function updateTable(response) {
        const tableBody = document.getElementById('analysis-table-body');
        
        if (!response.data || response.data.length === 0) {
            tableBody.innerHTML = '<tr><td colspan="7" class="text-center text-muted">Tidak ada data untuk ditampilkan</td></tr>';
            return;
        }
        
        let html = '';
        response.data.forEach((item, index) => {
            const statusClass = item.status === 'Ready' ? 'status-ready' : 'status-not-ready';
            const classificationClass = getClassificationClass(item.analysis_description);
            
            html += `
                <tr>
                    <td>${index + 1}</td>
                    <td>${item.part_name}</td>
                    <td>${item.part_code}</td>
                    <td class="text-end">${item.current_stock.toLocaleString('id-ID')}</td>
                    <td class="text-end">${item.in_stock.toLocaleString('id-ID')}</td>
                    <td class="text-end">${item.out_stock.toLocaleString('id-ID')}</td>
                    <td><span class="badge ${statusClass}">${item.status}</span></td>
                    <td><span class="badge ${classificationClass}">${item.analysis_description}</span></td>
                </tr>
            `;
        });
        
        tableBody.innerHTML = html;
    }
    
    function updateSummary(response) {
        const summaryInfo = document.getElementById('summary-info');
        summaryInfo.innerHTML = `
            Total Parts: ${response.total_parts} | 
            Periode: ${response.period_start} - ${response.period_end} | 
            Pengelompokan: ${response.grouping === 'week' ? 'Mingguan' : 'Bulanan'}
        `;
    }
    
    function getClassificationClass(classification) {
        const classMap = {
            'High Demand': 'classification-high-demand',
            'Stable': 'classification-stable',
            'Low Demand': 'classification-low-demand',
            'Seasonal': 'classification-seasonal',
            'Overstocked': 'classification-overstocked',
            'Uncategorized': 'classification-uncategorized',
            'Data Insufficient': 'classification-data-insufficient'
        };
        
        return classMap[classification] || 'classification-uncategorized';
    }
    
    // Add event listeners
    siteSelect.addEventListener('change', refreshData);
    startDateInput.addEventListener('change', refreshData);
    endDateInput.addEventListener('change', refreshData);
});
</script>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('contentsales'); ?>
<?php echo $__env->make('sales.partials.navigation', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

<div class="content">
    <div class="container-fluid">
        <div class="row">
            <div class="col-md-12">
                <div class="bgwhite shadow-kit rounded-lg mb-4">
                    <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                        <h5 class="mb-0 font-bold text-uppercase text-white">Part Analysis</h5>
                        <div id="summary-info" class="text-white" style="font-size: 11px;">
                            <?php if(isset($analysisData) && !empty($analysisData)): ?>
                                Total Parts: <?php echo e($analysisData['total_parts']); ?> | 
                                Periode: <?php echo e($analysisData['period_start']); ?> - <?php echo e($analysisData['period_end']); ?> | 
                                Pengelompokan: <?php echo e($analysisData['grouping'] === 'week' ? 'Mingguan' : 'Bulanan'); ?>

                            <?php else: ?>
                                Pilih site dan tanggal untuk melihat analisis
                            <?php endif; ?>
                        </div>
                    </div>
                    
                    <!-- Filter Section -->
                    <div class="card-body border-bottom">
                        <div class="row g-3">
                            <div class="col-md-4">
                                <label for="site-select" class="form-label">Site</label>
                                <select class="form-select" id="site-select" name="site_id">
                                    <option value="">Pilih Site</option>
                                    <?php $__currentLoopData = $sites; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $site): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e($site->site_id); ?>" <?php echo e($selectedSite == $site->site_id ? 'selected' : ''); ?>>
                                            <?php echo e($site->site_name); ?>

                                        </option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label for="start-date" class="form-label">Tanggal Mulai</label>
                                <input type="date" class="form-control" id="start-date" name="start_date" value="<?php echo e($startDate); ?>">
                            </div>
                            <div class="col-md-4">
                                <label for="end-date" class="form-label">Tanggal Akhir</label>
                                <input type="date" class="form-control" id="end-date" name="end_date" value="<?php echo e($endDate); ?>">
                            </div>
                        </div>
                        <div class="row mt-3">
                            <div class="col-12">
                                <div class="alert alert-info" style="font-size: 11px;">
                                    <strong>Informasi:</strong> 
                                    Jika rentang tanggal kurang dari 3 bulan, data akan dikelompokkan per minggu. 
                                    Jika 3 bulan atau lebih, data akan dikelompokkan per bulan. 
                                    Minimal 4 periode data diperlukan untuk klasifikasi yang bermakna.
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Table Section -->
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-bordered table-hover w-100">
                                <thead class="sticky-header" style="font-size: 11px;">
                                    <tr>
                                        <th>No</th>
                                        <th>Part Name</th>
                                        <th>Part Code</th>
                                        <th>Current Stock</th>
                                        <th>In Stock</th>
                                        <th>Out Stock</th>
                                        <th>Status</th>
                                        <th>Analysis Description</th>
                                    </tr>
                                </thead>
                                <tbody id="analysis-table-body" style="font-size: 11px;">
                                    <?php if(isset($analysisData) && !empty($analysisData['data'])): ?>
                                        <?php $__currentLoopData = $analysisData['data']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <tr>
                                            <td><?php echo e($index + 1); ?></td>
                                            <td><?php echo e($item['part_name']); ?></td>
                                            <td><?php echo e($item['part_code']); ?></td>
                                            <td class="text-end"><?php echo e(number_format($item['current_stock'], 0, ',', '.')); ?></td>
                                            <td class="text-end"><?php echo e(number_format($item['in_stock'], 0, ',', '.')); ?></td>
                                            <td class="text-end"><?php echo e(number_format($item['out_stock'], 0, ',', '.')); ?></td>
                                            <td>
                                                <span class="badge <?php echo e($item['status'] === 'Ready' ? 'status-ready' : 'status-not-ready'); ?>">
                                                    <?php echo e($item['status']); ?>

                                                </span>
                                            </td>
                                            <td>
                                                <?php
                                                    $classificationClass = '';
                                                    switch($item['analysis_description']) {
                                                        case 'High Demand':
                                                            $classificationClass = 'classification-high-demand';
                                                            break;
                                                        case 'Stable':
                                                            $classificationClass = 'classification-stable';
                                                            break;
                                                        case 'Low Demand':
                                                            $classificationClass = 'classification-low-demand';
                                                            break;
                                                        case 'Seasonal':
                                                            $classificationClass = 'classification-seasonal';
                                                            break;
                                                        case 'Overstocked':
                                                            $classificationClass = 'classification-overstocked';
                                                            break;
                                                        case 'Data Insufficient':
                                                            $classificationClass = 'classification-data-insufficient';
                                                            break;
                                                        default:
                                                            $classificationClass = 'classification-uncategorized';
                                                    }
                                                ?>
                                                <span class="badge <?php echo e($classificationClass); ?>">
                                                    <?php echo e($item['analysis_description']); ?>

                                                </span>
                                            </td>
                                        </tr>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    <?php else: ?>
                                        <tr>
                                            <td colspan="8" class="text-center text-muted">Pilih site dan tanggal untuk melihat analisis part</td>
                                        </tr>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('sales.contentsales', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\portalpwb\resources\views/sales/part-analysis.blade.php ENDPATH**/ ?>