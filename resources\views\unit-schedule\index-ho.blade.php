@extends('warehouse.content')

@section('contentwho')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="{{ route('adminho.dashboard') }}">Dashboard</a></li>
                        <li class="breadcrumb-item active">Unit Schedule</li>
                    </ol>
                </div>
                <h4 class="page-title">Unit Schedule - Head Office</h4>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Data Table Section (Full Width) -->
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-md-10">
                            <h4 class="header-title">Jadwal Service Unit - Semua Site</h4>
                            <p class="text-muted font-13 mb-4">
                                Prediksi jadwal service berdasarkan Hour Meter (HM) dengan interval 250 HM
                            </p>
                        </div>
                        <div class="col-md-2 text-md-end">
                            <div class="btn-group" role="group">
                                <button type="button" class="btn btn-sm btn-success" id="export-excel-btn">
                                    <i class="mdi mdi-file-excel me-1"></i> Export Excel
                                </button>
                                <button type="button" class="btn btn-sm btn-primary" id="refresh-btn">
                                    <i class="mdi mdi-refresh me-1"></i> Refresh
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Search and Filter Section -->
                    <div class="row mb-3">
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="site-filter">Filter Site</label>
                                <select class="form-control" id="site-filter">
                                    <option value="">Semua Site</option>
                                    @foreach($sites as $site)
                                        <option value="{{ $site->site_id }}">{{ $site->site_name }}</option>
                                    @endforeach
                                </select>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="search-input">Cari Unit</label>
                                <input type="text" class="form-control" id="search-input" placeholder="Cari berdasarkan kode unit atau tipe unit...">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>&nbsp;</label>
                                <div class="d-flex gap-2">
                                    <button type="button" class="btn btn-outline-primary" id="filter-all">Semua</button>
                                    <button type="button" class="btn btn-outline-danger" id="filter-overdue">Terlambat</button>
                                    <button type="button" class="btn btn-outline-secondary" id="filter-due-soon">Segera</button>
                                    <button type="button" class="btn btn-outline-success" id="filter-normal">Normal</button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Loading Skeleton -->
                    <div id="loading-skeleton" class="d-none">
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Unit Code</th>
                                        <th>Unit Type</th>
                                        <th>Site</th>
                                        <th>Current HM</th>
                                        <th>Last Service</th>
                                        <th>Next Service HM</th>
                                        <th>Remaining HM</th>
                                        <th>Days Until Service</th>
                                        <th>Predicted Date</th>
                                        <th>Status</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @for($i = 0; $i < 5; $i++)
                                    <tr>
                                        <td><div class="placeholder-glow"><span class="placeholder col-8"></span></div></td>
                                        <td><div class="placeholder-glow"><span class="placeholder col-6"></span></div></td>
                                        <td><div class="placeholder-glow"><span class="placeholder col-6"></span></div></td>
                                        <td><div class="placeholder-glow"><span class="placeholder col-4"></span></div></td>
                                        <td><div class="placeholder-glow"><span class="placeholder col-6"></span></div></td>
                                        <td><div class="placeholder-glow"><span class="placeholder col-4"></span></div></td>
                                        <td><div class="placeholder-glow"><span class="placeholder col-4"></span></div></td>
                                        <td><div class="placeholder-glow"><span class="placeholder col-3"></span></div></td>
                                        <td><div class="placeholder-glow"><span class="placeholder col-6"></span></div></td>
                                        <td><div class="placeholder-glow"><span class="placeholder col-5"></span></div></td>
                                    </tr>
                                    @endfor
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- Data Table -->
                    <div class="table-responsive" id="data-table-container" style="display: none;">
                        <table class="table table-striped table-hover" id="unit-schedule-table">
                            <thead class="table-dark">
                                <tr>
                                    <th>Unit Code</th>
                                    <th>Unit Type</th>
                                    <th>Site</th>
                                    <th>Current HM</th>
                                    <th>Last Service</th>
                                    <th>Next Service HM</th>
                                    <th>Remaining HM</th>
                                    <th>Days Until Service</th>
                                    <th>Predicted Date</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody id="unit-schedule-tbody">
                                <!-- Data will be loaded here -->
                            </tbody>
                        </table>
                    </div>

                    <!-- Empty State -->
                    <div id="empty-state" class="text-center py-5" style="display: none;">
                        <i class="mdi mdi-calendar-clock text-muted" style="font-size: 64px;"></i>
                        <h5 class="text-muted mt-3">Tidak ada data jadwal unit</h5>
                        <p class="text-muted">Belum ada data Hour Meter untuk unit yang dipilih.</p>
                    </div>

                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@section('resourcewho')
@vite('resources/js/unit-schedule-ho.js')
<script>
    window.unitScheduleConfig = {
        dataUrl: '{{ route("warehouse.unit-schedule.data") }}',
        exportUrl: '{{ route("warehouse.unit-schedule.export") }}',
        csrfToken: '{{ csrf_token() }}'
    };
</script>
@endsection
